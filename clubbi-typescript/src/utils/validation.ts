import Joi from 'joi';

export const userSignupSchema = Joi.object({
  username: Joi.string().min(3).max(30).required(),
  email: Joi.string().email().required(),
  mobile_number: Joi.string().pattern(/^\d{10}$/).optional(),
  password: Joi.string().min(6).required(),
});

export const userLoginSchema = Joi.object({
  username: Joi.string().pattern(/^(?:\d{10}|[\w\.-]+@[\w\.-]+\.\w+)$/).required()
    .messages({
      'string.pattern.base': 'Enter a valid email or 10-digit mobile number'
    }),
  password: Joi.string().required(),
});

export const clubSchema = Joi.object({
  name: Joi.string().max(100).required(),
  description: Joi.string().required(),
  address: Joi.string().max(200).required(),
  google_maps_link: Joi.string().uri().optional().allow(''),
  rating: Joi.number().min(0).max(5).precision(1).required(),
  image_url: Joi.string().uri().required(),
  additional_images: Joi.array().items(Joi.string().uri()).optional(),
  opening_hours: Joi.object().optional(),
  top_pick: Joi.boolean().optional(),
  offers: Joi.boolean().optional(),
  nearby: Joi.boolean().optional(),
  lower_price: Joi.boolean().optional(),
});

export const clubFilterSchema = Joi.object({
  rating: Joi.number().min(0).max(5).optional(),
  top_pick: Joi.boolean().optional(),
  offers: Joi.boolean().optional(),
  nearby: Joi.boolean().optional(),
  lower_price: Joi.boolean().optional(),
});

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[\w\.-]+@[\w\.-]+\.\w+$/;
  return emailRegex.test(email);
};

export const isValidMobile = (mobile: string): boolean => {
  const mobileRegex = /^\d{10}$/;
  return mobileRegex.test(mobile);
};
