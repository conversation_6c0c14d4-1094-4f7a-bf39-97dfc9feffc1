import { Request, Response } from 'express';
import { AuthService } from '../services/authService';
import { ClubService } from '../services/clubService';
import { SessionRequest } from '../middleware/session';

export class AdminController {
  private authService = new AuthService();
  private clubService = new ClubService();

  adminLogin = async (req: SessionRequest, res: Response): Promise<void> => {
    if (req.method === 'GET') {
      const messages = req.session?.messages || [];
      req.session!.messages = []; // Clear messages after displaying
      res.render('login', { messages });
      return;
    }

    try {
      const { username, password } = req.body;

      if (!username || !password) {
        req.session!.messages = [{ type: 'error', message: 'Username and password are required.' }];
        res.redirect('/');
        return;
      }

      const user = await this.authService.authenticateAdmin(username, password);

      if (!user) {
        req.session!.messages = [{ type: 'error', message: 'Invalid credentials or you do not have admin access.' }];
        res.redirect('/');
        return;
      }

      req.session!.userId = user.id;
      res.redirect('/dashboard/');
    } catch (error) {
      console.error('Admin login error:', error);
      req.session!.messages = [{ type: 'error', message: 'An error occurred during login.' }];
      res.redirect('/');
    }
  };

  adminDashboard = async (req: SessionRequest, res: Response): Promise<void> => {
    try {
      const clubs = await this.clubService.getAllClubs();
      res.render('admin_dashboard', { clubs, user: req.user });
    } catch (error) {
      console.error('Dashboard error:', error);
      res.status(500).send('Internal server error');
    }
  };

  addClub = async (req: SessionRequest, res: Response): Promise<void> => {
    if (req.method === 'GET') {
      res.render('club_form', { club: null });
      return;
    }

    try {
      const clubData = {
        name: req.body.name,
        description: req.body.description,
        address: req.body.address,
        google_maps_link: req.body.google_maps_link || '',
        rating: parseFloat(req.body.rating),
        image_url: req.body.image_url,
        opening_hours: req.body.opening_hours ? JSON.parse(req.body.opening_hours) : {},
        top_pick: req.body.top_pick === 'on',
        offers: req.body.offers === 'on',
        nearby: req.body.nearby === 'on',
        lower_price: req.body.lower_price === 'on',
      };

      await this.clubService.createClub(clubData);
      res.redirect('/dashboard/');
    } catch (error) {
      console.error('Add club error:', error);
      res.status(500).send('Error adding club');
    }
  };

  updateClub = async (req: SessionRequest, res: Response): Promise<void> => {
    const clubId = parseInt(req.params.club_id);

    if (req.method === 'GET') {
      try {
        const club = await this.clubService.getClubById(clubId);
        if (!club) {
          res.status(404).send('Club not found');
          return;
        }
        res.render('club_form', { club });
      } catch (error) {
        console.error('Get club error:', error);
        res.status(500).send('Error loading club');
      }
      return;
    }

    try {
      const clubData = {
        name: req.body.name,
        description: req.body.description,
        address: req.body.address,
        google_maps_link: req.body.google_maps_link || '',
        rating: parseFloat(req.body.rating),
        image_url: req.body.image_url,
        opening_hours: req.body.opening_hours ? JSON.parse(req.body.opening_hours) : {},
        top_pick: req.body.top_pick === 'on',
        offers: req.body.offers === 'on',
        nearby: req.body.nearby === 'on',
        lower_price: req.body.lower_price === 'on',
      };

      const updatedClub = await this.clubService.updateClub(clubId, clubData);
      if (!updatedClub) {
        res.status(404).send('Club not found');
        return;
      }

      res.redirect('/dashboard/');
    } catch (error) {
      console.error('Update club error:', error);
      res.status(500).send('Error updating club');
    }
  };

  deleteClub = async (req: SessionRequest, res: Response): Promise<void> => {
    try {
      const clubId = parseInt(req.params.club_id);
      const success = await this.clubService.deleteClub(clubId);
      
      if (!success) {
        res.status(404).send('Club not found');
        return;
      }

      res.redirect('/dashboard/');
    } catch (error) {
      console.error('Delete club error:', error);
      res.status(500).send('Error deleting club');
    }
  };

  logout = (req: SessionRequest, res: Response): void => {
    req.session?.destroy((err) => {
      if (err) {
        console.error('Session destroy error:', err);
      }
      res.redirect('/');
    });
  };
}
