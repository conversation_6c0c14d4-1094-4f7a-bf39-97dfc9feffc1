import { AppDataSource } from '../config/database';
import { Club } from '../models/Club';
import dotenv from 'dotenv';

dotenv.config();

const sampleClubs = [
  {
    name: 'Elite Sports Club',
    description: 'Premium sports and fitness facility with state-of-the-art equipment and professional trainers.',
    address: '123 Main Street, Downtown',
    google_maps_link: 'https://maps.google.com/elite-sports-club',
    rating: 4.8,
    image_url: 'https://example.com/images/elite-sports.jpg',
    additional_images: [
      'https://example.com/images/elite-sports-gym.jpg',
      'https://example.com/images/elite-sports-pool.jpg'
    ],
    opening_hours: {
      monday: '6:00 AM - 10:00 PM',
      tuesday: '6:00 AM - 10:00 PM',
      wednesday: '6:00 AM - 10:00 PM',
      thursday: '6:00 AM - 10:00 PM',
      friday: '6:00 AM - 10:00 PM',
      saturday: '8:00 AM - 8:00 PM',
      sunday: '8:00 AM - 6:00 PM'
    },
    top_pick: true,
    offers: true,
    nearby: true,
    lower_price: false
  },
  {
    name: 'Sunset Beach Club',
    description: 'Relaxing beachside club with beautiful ocean views, perfect for unwinding after a long day.',
    address: '456 Ocean Drive, Beachfront',
    google_maps_link: 'https://maps.google.com/sunset-beach-club',
    rating: 4.5,
    image_url: 'https://example.com/images/sunset-beach.jpg',
    additional_images: [
      'https://example.com/images/sunset-beach-bar.jpg',
      'https://example.com/images/sunset-beach-deck.jpg'
    ],
    opening_hours: {
      monday: '10:00 AM - 11:00 PM',
      tuesday: '10:00 AM - 11:00 PM',
      wednesday: '10:00 AM - 11:00 PM',
      thursday: '10:00 AM - 11:00 PM',
      friday: '10:00 AM - 12:00 AM',
      saturday: '10:00 AM - 12:00 AM',
      sunday: '10:00 AM - 10:00 PM'
    },
    top_pick: false,
    offers: false,
    nearby: false,
    lower_price: false
  },
  {
    name: 'Budget Fitness Center',
    description: 'Affordable fitness center with all the essential equipment for your workout needs.',
    address: '789 Budget Lane, Affordable District',
    google_maps_link: 'https://maps.google.com/budget-fitness',
    rating: 3.8,
    image_url: 'https://example.com/images/budget-fitness.jpg',
    additional_images: [
      'https://example.com/images/budget-fitness-weights.jpg'
    ],
    opening_hours: {
      monday: '5:00 AM - 11:00 PM',
      tuesday: '5:00 AM - 11:00 PM',
      wednesday: '5:00 AM - 11:00 PM',
      thursday: '5:00 AM - 11:00 PM',
      friday: '5:00 AM - 11:00 PM',
      saturday: '7:00 AM - 9:00 PM',
      sunday: '7:00 AM - 7:00 PM'
    },
    top_pick: false,
    offers: true,
    nearby: true,
    lower_price: true
  }
];

const seedClubs = async () => {
  try {
    await AppDataSource.initialize();
    console.log('Database connected');

    const clubRepository = AppDataSource.getRepository(Club);

    // Check if clubs already exist
    const existingClubs = await clubRepository.count();
    if (existingClubs > 0) {
      console.log('Clubs already exist in database');
      process.exit(0);
    }

    // Create sample clubs
    for (const clubData of sampleClubs) {
      const club = new Club();
      Object.assign(club, clubData);
      await clubRepository.save(club);
      console.log(`Created club: ${club.name}`);
    }

    console.log('Sample clubs created successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding clubs:', error);
    process.exit(1);
  }
};

seedClubs();
