import { AppDataSource } from '../config/database';
import { Club } from '../models/Club';
import { ClubRequest, ClubFilterQuery } from '../types';
import { Like, MoreThanOrEqual } from 'typeorm';

export class ClubService {
  private clubRepository = AppDataSource.getRepository(Club);

  async createClub(clubData: ClubRequest): Promise<Club> {
    const club = new Club();
    Object.assign(club, clubData);
    return this.clubRepository.save(club);
  }

  async getAllClubs(): Promise<Club[]> {
    return this.clubRepository.find();
  }

  async getClubById(id: number): Promise<Club | null> {
    return this.clubRepository.findOne({ where: { id } });
  }

  async updateClub(id: number, clubData: Partial<ClubRequest>): Promise<Club | null> {
    const club = await this.clubRepository.findOne({ where: { id } });
    if (!club) {
      return null;
    }

    Object.assign(club, clubData);
    return this.clubRepository.save(club);
  }

  async deleteClub(id: number): Promise<boolean> {
    const result = await this.clubRepository.delete(id);
    return result.affected !== 0;
  }

  async searchClubs(query: string): Promise<Club[]> {
    return this.clubRepository.find({
      where: {
        name: Like(`%${query}%`)
      }
    });
  }

  async filterClubs(filters: ClubFilterQuery): Promise<Club[]> {
    const whereConditions: any = {};

    if (filters.rating !== undefined) {
      whereConditions.rating = MoreThanOrEqual(filters.rating);
    }

    if (filters.top_pick !== undefined) {
      whereConditions.top_pick = filters.top_pick;
    }

    if (filters.offers !== undefined) {
      whereConditions.offers = filters.offers;
    }

    if (filters.nearby !== undefined) {
      whereConditions.nearby = filters.nearby;
    }

    if (filters.lower_price !== undefined) {
      whereConditions.lower_price = filters.lower_price;
    }

    return this.clubRepository.find({
      where: whereConditions
    });
  }
}
