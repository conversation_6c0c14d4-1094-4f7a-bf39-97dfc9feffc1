import { Router } from 'express';
import { AdminController } from '../controllers/adminController';
import { requireAdminSession, loadUser } from '../middleware/session';

const router = Router();
const adminController = new AdminController();

// Admin login page (homepage)
router.get('/', adminController.adminLogin);
router.post('/', adminController.adminLogin);

// Admin dashboard routes
router.get('/dashboard', requireAdminSession, adminController.adminDashboard);

// Club management routes
router.get('/add-club', requireAdminSession, adminController.addClub);
router.post('/add-club', requireAdminSession, adminController.addClub);

router.get('/update-club/:club_id', requireAdminSession, adminController.updateClub);
router.post('/update-club/:club_id', requireAdminSession, adminController.updateClub);

router.get('/delete-club/:club_id', requireAdminSession, adminController.deleteClub);

// Logout
router.get('/logout', adminController.logout);

export default router;
