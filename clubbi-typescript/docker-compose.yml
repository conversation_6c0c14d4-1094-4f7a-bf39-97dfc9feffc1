version: '3.8'

services:
  typescript-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: clubbi_typescript_app
    working_dir: /app
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_USERNAME=clubbidevuser
      - DATABASE_PASSWORD=clubbi%^*user1234$
      - DATABASE_NAME=clubbi
      - JWT_SECRET=your-super-secret-jwt-key-here-typescript
      - SESSION_SECRET=your-session-secret-here-typescript
      - PORT=8000
    command: ["npm", "run", "dev"]
    ports:
      - "8001:8000"  # Different port to avoid conflict with Django app
    networks:
      - clubbi-ts
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:16
    container_name: clubbi_typescript_db
    environment:
      POSTGRES_USER: clubbidevuser
      POSTGRES_PASSWORD: clubbi%^*user1234$
      POSTGRES_DB: clubbi
    volumes:
      - postgres_db_clubbi_ts:/var/lib/postgresql/data
    ports:
      - "5433:5432"  # Different port to avoid conflict with Django DB
    networks:
      - clubbi-ts
    restart: unless-stopped

volumes:
  postgres_db_clubbi_ts:

networks:
  clubbi-ts:
    driver: bridge
