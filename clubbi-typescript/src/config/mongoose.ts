import mongoose from 'mongoose';
import dotenv from 'dotenv';

dotenv.config();

// MongoDB connection configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/clubbi';
const NODE_ENV = process.env.NODE_ENV || 'development';

// Connection options
const mongooseOptions: mongoose.ConnectOptions = {
  // Connection pool settings
  maxPoolSize: 10, // Maintain up to 10 socket connections
  serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
  socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
  
  // Buffering settings
  bufferMaxEntries: 0, // Disable mongoose buffering
  bufferCommands: false, // Disable mongoose buffering
  
  // Other options
  retryWrites: true,
  w: 'majority'
};

// Connection event handlers
mongoose.connection.on('connected', () => {
  console.log(`Mongoose connected to ${MONGODB_URI}`);
});

mongoose.connection.on('error', (err) => {
  console.error('Mongoose connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('Mongoose disconnected');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  try {
    await mongoose.connection.close();
    console.log('Mongoose connection closed through app termination');
    process.exit(0);
  } catch (error) {
    console.error('Error during mongoose shutdown:', error);
    process.exit(1);
  }
});

// Initialize database connection
export const initializeMongoose = async (): Promise<void> => {
  try {
    // Set mongoose options
    mongoose.set('strictQuery', false); // Prepare for Mongoose 7
    
    if (NODE_ENV === 'development') {
      mongoose.set('debug', true); // Enable debug mode in development
    }
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, mongooseOptions);
    console.log('MongoDB connection established successfully');
    
  } catch (error) {
    console.error('Error during MongoDB initialization:', error);
    process.exit(1);
  }
};

// Export mongoose instance for direct use if needed
export { mongoose };

// Health check function
export const checkMongooseConnection = (): boolean => {
  return mongoose.connection.readyState === 1; // 1 = connected
};

// Get connection status
export const getConnectionStatus = (): string => {
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting'
  };
  return states[mongoose.connection.readyState as keyof typeof states] || 'unknown';
};
