import { Request, Response, NextFunction } from "express";
import { verifyToken } from "../utils/jwt";
import { JwtPayload } from "../types";
import { User } from "../models/mongoose/User";
import mongoose from "mongoose";

export interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  const authHeader = req.headers["authorization"];
  const token = authHeader && authHeader.split(" ")[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ error: "Access token required" });
  }

  try {
    // Verify JWT token
    const decoded = verifyToken(token);

    // Validate user exists in database and is active
    const user = await User.findById(decoded.userId)
      .select("-password") // Exclude password from result
      .lean(); // Return plain JavaScript object for better performance

    if (!user) {
      return res.status(403).json({ error: "User not found" });
    }

    if (user.is_deleted) {
      return res.status(403).json({ error: "Account has been deactivated" });
    }

    if (!user.is_active) {
      return res.status(403).json({ error: "Account is not active" });
    }

    // Update JWT payload with fresh user data from database
    req.user = {
      userId: user._id.toString(),
      username: user.username,
      email: user.email,
      is_admin: user.is_admin,
    };

    next();
  } catch (error) {
    if (error instanceof mongoose.Error) {
      console.error("Database error during authentication:", error);
      return res.status(500).json({ error: "Internal server error" });
    }
    return res.status(403).json({ error: "Invalid or expired token" });
  }
};

export const requireAdmin = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  if (!req.user) {
    return res.status(401).json({ error: "Authentication required" });
  }

  try {
    // Double-check admin status from database for security
    const user = await User.findById(req.user.userId)
      .select("is_admin is_superuser is_active is_deleted")
      .lean();

    if (!user) {
      return res.status(403).json({ error: "User not found" });
    }

    if (user.is_deleted || !user.is_active) {
      return res.status(403).json({ error: "Account is not active" });
    }

    if (!user.is_admin && !user.is_superuser) {
      return res.status(403).json({ error: "Admin access required" });
    }

    next();
  } catch (error) {
    console.error("Database error during admin check:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

export const optionalAuth = async (
  req: AuthenticatedRequest,
  _res: Response, // Prefixed with underscore to indicate intentionally unused
  next: NextFunction
) => {
  const authHeader = req.headers["authorization"];
  const token = authHeader && authHeader.split(" ")[1];

  if (token) {
    try {
      // Verify JWT token
      const decoded = verifyToken(token);

      // Validate user exists in database and is active
      const user = await User.findById(decoded.userId)
        .select("-password")
        .lean();

      if (user && !user.is_deleted && user.is_active) {
        // Update JWT payload with fresh user data from database
        req.user = {
          userId: user._id.toString(),
          username: user.username,
          email: user.email,
          is_admin: user.is_admin,
        };
      }
      // If user doesn't exist or is inactive, we continue without authentication
    } catch (error) {
      // Token is invalid or database error, but we continue without authentication
      console.error("Error in optional authentication:", error);
    }
  }

  next();
};
