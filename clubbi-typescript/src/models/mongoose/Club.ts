import mongoose, { Document, Schema } from 'mongoose';

// Interface for Club document
export interface IClub extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  address: string;
  google_maps_link?: string;
  rating: number;
  image_url: string;
  additional_images: string[];
  opening_hours: Record<string, any>;
  top_pick: boolean;
  offers: boolean;
  nearby: boolean;
  lower_price: boolean;
  created_at: Date;
  updated_at: Date;
}

// Club Schema
const ClubSchema = new Schema<IClub>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  address: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  google_maps_link: {
    type: String,
    trim: true,
    default: '',
    validate: {
      validator: function(v: string) {
        if (!v) return true; // Allow empty string
        // Basic URL validation
        return /^https?:\/\/.+/.test(v);
      },
      message: 'Please enter a valid URL'
    }
  },
  rating: {
    type: Number,
    required: true,
    min: 0,
    max: 5,
    validate: {
      validator: function(v: number) {
        // Allow one decimal place
        return Number.isInteger(v * 10);
      },
      message: 'Rating must have at most one decimal place'
    }
  },
  image_url: {
    type: String,
    required: true,
    trim: true,
    validate: {
      validator: function(v: string) {
        // Basic URL validation
        return /^https?:\/\/.+/.test(v);
      },
      message: 'Please enter a valid image URL'
    }
  },
  additional_images: {
    type: [String],
    default: [],
    validate: {
      validator: function(images: string[]) {
        // Validate each URL in the array
        return images.every(url => /^https?:\/\/.+/.test(url));
      },
      message: 'All additional images must be valid URLs'
    }
  },
  opening_hours: {
    type: Schema.Types.Mixed,
    default: {}
  },
  top_pick: {
    type: Boolean,
    default: false
  },
  offers: {
    type: Boolean,
    default: false
  },
  nearby: {
    type: Boolean,
    default: false
  },
  lower_price: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  collection: 'clubs'
});

// Indexes for better query performance
ClubSchema.index({ name: 1 });
ClubSchema.index({ rating: -1 });
ClubSchema.index({ top_pick: 1 });
ClubSchema.index({ offers: 1 });
ClubSchema.index({ nearby: 1 });
ClubSchema.index({ lower_price: 1 });
ClubSchema.index({ created_at: -1 });

// Compound indexes for common filter combinations
ClubSchema.index({ top_pick: 1, rating: -1 });
ClubSchema.index({ offers: 1, rating: -1 });
ClubSchema.index({ nearby: 1, rating: -1 });
ClubSchema.index({ lower_price: 1, rating: -1 });

// Static methods for common queries
ClubSchema.statics.findByRating = function(minRating: number) {
  return this.find({ rating: { $gte: minRating } }).sort({ rating: -1 });
};

ClubSchema.statics.findTopPicks = function() {
  return this.find({ top_pick: true }).sort({ rating: -1 });
};

ClubSchema.statics.findWithOffers = function() {
  return this.find({ offers: true }).sort({ rating: -1 });
};

ClubSchema.statics.findNearby = function() {
  return this.find({ nearby: true }).sort({ rating: -1 });
};

ClubSchema.statics.findLowerPrice = function() {
  return this.find({ lower_price: true }).sort({ rating: -1 });
};

ClubSchema.statics.findWithFilters = function(filters: {
  rating?: number;
  top_pick?: boolean;
  offers?: boolean;
  nearby?: boolean;
  lower_price?: boolean;
}) {
  const query: any = {};
  
  if (filters.rating !== undefined) {
    query.rating = { $gte: filters.rating };
  }
  if (filters.top_pick !== undefined) {
    query.top_pick = filters.top_pick;
  }
  if (filters.offers !== undefined) {
    query.offers = filters.offers;
  }
  if (filters.nearby !== undefined) {
    query.nearby = filters.nearby;
  }
  if (filters.lower_price !== undefined) {
    query.lower_price = filters.lower_price;
  }
  
  return this.find(query).sort({ rating: -1 });
};

// Create and export the model
export const Club = mongoose.model<IClub>('Club', ClubSchema);
