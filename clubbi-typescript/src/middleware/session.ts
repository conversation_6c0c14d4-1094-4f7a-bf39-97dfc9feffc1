import { Request, Response, NextFunction } from 'express';
import { User } from '../models/User';
import { AppDataSource } from '../config/database';

export interface SessionRequest extends Request {
  user?: User;
  flash?: (type: string, message: string) => void;
}

export const requireLogin = (req: SessionRequest, res: Response, next: NextFunction) => {
  if (!req.session || !req.session.userId) {
    return res.redirect('/');
  }
  next();
};

export const requireAdminSession = async (req: SessionRequest, res: Response, next: NextFunction) => {
  if (!req.session || !req.session.userId) {
    return res.redirect('/');
  }

  try {
    const userRepository = AppDataSource.getRepository(User);
    const user = await userRepository.findOne({ where: { id: req.session.userId } });
    
    if (!user || (!user.is_admin && !user.is_superuser)) {
      return res.status(403).send('You are not authorized to access this page.');
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Error checking admin access:', error);
    return res.status(500).send('Internal server error');
  }
};

export const loadUser = async (req: SessionRequest, res: Response, next: NextFunction) => {
  if (req.session && req.session.userId) {
    try {
      const userRepository = AppDataSource.getRepository(User);
      const user = await userRepository.findOne({ where: { id: req.session.userId } });
      if (user) {
        req.user = user;
      }
    } catch (error) {
      console.error('Error loading user:', error);
    }
  }
  next();
};

declare module 'express-session' {
  interface SessionData {
    userId?: number;
    messages?: { type: string; message: string }[];
  }
}
