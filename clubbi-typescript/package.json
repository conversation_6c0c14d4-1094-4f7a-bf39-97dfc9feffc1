{"name": "clubbi-typescript", "version": "1.0.0", "description": "TypeScript/Node.js recreation of Django Clubbi application", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert", "seed:admin": "ts-node src/scripts/createAdmin.ts", "seed:clubs": "ts-node src/scripts/seedClubs.ts", "seed:all": "npm run seed:admin && npm run seed:clubs"}, "keywords": ["typescript", "express", "postgresql", "typeorm", "jwt"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "typeorm": "^0.3.17", "pg": "^8.11.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "dotenv": "^16.3.1", "express-session": "^1.17.3", "connect-flash": "^0.1.1", "ejs": "^3.1.9"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.4", "@types/pg": "^8.10.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/cors": "^2.8.17", "@types/express-session": "^1.17.10", "@types/connect-flash": "^0.0.40", "typescript": "^5.3.3", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0"}}