import { Request, Response } from 'express';
import { ClubService } from '../services/clubService';
import { clubSchema, clubFilterSchema } from '../utils/validation';
import { ClubFilterQuery } from '../types';

export class ClubController {
  private clubService = new ClubService();

  searchClubs = async (req: Request, res: Response): Promise<void> => {
    try {
      const query = req.query.query as string || '';
      const clubs = await this.clubService.searchClubs(query);
      res.status(200).json(clubs);
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };

  filterClubs = async (req: Request, res: Response): Promise<void> => {
    try {
      const filters: ClubFilterQuery = {};

      if (req.query.rating) {
        filters.rating = parseFloat(req.query.rating as string);
      }

      if (req.query.top_pick !== undefined) {
        filters.top_pick = req.query.top_pick === 'true';
      }

      if (req.query.offers !== undefined) {
        filters.offers = req.query.offers === 'true';
      }

      if (req.query.nearby !== undefined) {
        filters.nearby = req.query.nearby === 'true';
      }

      if (req.query.lower_price !== undefined) {
        filters.lower_price = req.query.lower_price === 'true';
      }

      const { error } = clubFilterSchema.validate(filters);
      if (error) {
        res.status(400).json({ error: error.details[0].message });
        return;
      }

      const clubs = await this.clubService.filterClubs(filters);
      res.status(200).json(clubs);
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };

  getClubDetail = async (req: Request, res: Response): Promise<void> => {
    try {
      const clubId = parseInt(req.params.club_id);
      
      if (isNaN(clubId)) {
        res.status(400).json({ error: 'Invalid club ID' });
        return;
      }

      const club = await this.clubService.getClubById(clubId);
      
      if (!club) {
        res.status(404).json({ error: 'Club not found' });
        return;
      }

      res.status(200).json(club);
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };

  createClub = async (req: Request, res: Response): Promise<void> => {
    try {
      const { error, value } = clubSchema.validate(req.body);
      
      if (error) {
        res.status(400).json({ error: error.details[0].message });
        return;
      }

      const club = await this.clubService.createClub(value);
      res.status(201).json(club);
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };

  updateClub = async (req: Request, res: Response): Promise<void> => {
    try {
      const clubId = parseInt(req.params.club_id);
      
      if (isNaN(clubId)) {
        res.status(400).json({ error: 'Invalid club ID' });
        return;
      }

      const { error, value } = clubSchema.validate(req.body);
      
      if (error) {
        res.status(400).json({ error: error.details[0].message });
        return;
      }

      const club = await this.clubService.updateClub(clubId, value);
      
      if (!club) {
        res.status(404).json({ error: 'Club not found' });
        return;
      }

      res.status(200).json(club);
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };

  deleteClub = async (req: Request, res: Response): Promise<void> => {
    try {
      const clubId = parseInt(req.params.club_id);
      
      if (isNaN(clubId)) {
        res.status(400).json({ error: 'Invalid club ID' });
        return;
      }

      const success = await this.clubService.deleteClub(clubId);
      
      if (!success) {
        res.status(404).json({ error: 'Club not found' });
        return;
      }

      res.status(204).send();
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };

  getAllClubs = async (req: Request, res: Response): Promise<void> => {
    try {
      const clubs = await this.clubService.getAllClubs();
      res.status(200).json(clubs);
    } catch (error) {
      res.status(500).json({ error: 'Internal server error' });
    }
  };
}
