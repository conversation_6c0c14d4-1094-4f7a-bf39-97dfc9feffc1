# Authentication Middleware Migration to Mongoose

This document explains the migration of the authentication middleware from TypeORM to Mongoose, including all changes made and how to use the new implementation.

## Overview of Changes

The authentication middleware has been completely rewritten to use Mongoose instead of TypeORM while maintaining the same functionality and improving security through database-backed validation.

## Key Changes Made

### 1. Enhanced Authentication Logic

**Before (TypeORM):**
- Only verified JWT tokens
- No database validation during authentication
- Relied solely on token data

**After (Mongoose):**
- Verifies JWT tokens AND validates against database
- Checks user existence, active status, and deletion status
- Provides fresh user data from database
- Better error handling with specific error messages

### 2. Database Integration

**New Features:**
- Real-time user status validation
- Database-backed admin permission checks
- Automatic user data refresh from database
- Proper error handling for database operations

### 3. Security Improvements

- **Double verification**: JWT + database validation
- **Status checks**: Validates `is_active` and `is_deleted` flags
- **Fresh data**: Always uses current user data from database
- **Admin verification**: Double-checks admin status from database for security

## Files Created/Modified

### New Files Created:

1. **`src/models/mongoose/User.ts`**
   - Mongoose User model with full schema definition
   - Password hashing with bcrypt
   - Validation rules and indexes
   - Instance and static methods

2. **`src/models/mongoose/Club.ts`**
   - Mongoose Club model for consistency
   - Proper validation and indexes
   - Static methods for common queries

3. **`src/config/mongoose.ts`**
   - MongoDB connection configuration
   - Connection event handlers
   - Graceful shutdown handling
   - Health check functions

4. **`src/models/mongoose/index.ts`**
   - Central export file for all Mongoose models

### Modified Files:

1. **`src/middleware/auth.ts`**
   - Complete rewrite with Mongoose integration
   - Enhanced error handling
   - Database validation for all middleware functions

2. **`src/types/index.ts`**
   - Updated ID types from `number` to `string` for MongoDB ObjectIds
   - Maintained backward compatibility for all other fields

## New Middleware Functions

### 1. `authenticateToken` (Enhanced)

```typescript
export const authenticateToken = async (req: AuthenticatedRequest, res: Response, next: NextFunction)
```

**New Features:**
- Validates JWT token
- Checks user exists in database
- Verifies user is not deleted (`is_deleted: false`)
- Verifies user is active (`is_active: true`)
- Updates request with fresh user data from database
- Proper error handling for database operations

**Error Responses:**
- `401`: "Access token required" (no token)
- `403`: "User not found" (user doesn't exist)
- `403`: "Account has been deactivated" (user is deleted)
- `403`: "Account is not active" (user is inactive)
- `403`: "Invalid or expired token" (JWT verification failed)
- `500`: "Internal server error" (database error)

### 2. `requireAdmin` (Enhanced)

```typescript
export const requireAdmin = async (req: AuthenticatedRequest, res: Response, next: NextFunction)
```

**New Features:**
- Requires prior authentication
- Double-checks admin status from database
- Validates user account status
- Supports both `is_admin` and `is_superuser` flags

**Error Responses:**
- `401`: "Authentication required" (no user in request)
- `403`: "User not found" (user doesn't exist)
- `403`: "Account is not active" (user is deleted/inactive)
- `403`: "Admin access required" (user is not admin/superuser)
- `500`: "Internal server error" (database error)

### 3. `optionalAuth` (Enhanced)

```typescript
export const optionalAuth = async (req: AuthenticatedRequest, res: Response, next: NextFunction)
```

**New Features:**
- Validates JWT token if present
- Checks user status in database
- Gracefully handles errors (continues without auth)
- Updates request with fresh user data if valid

**Behavior:**
- If token is valid and user is active: sets `req.user`
- If token is invalid or user is inactive: continues without `req.user`
- Never blocks the request (always calls `next()`)

## Usage Examples

### Basic Route Protection

```typescript
import { authenticateToken, requireAdmin } from '../middleware/auth';

// Protect a route with authentication
router.get('/profile', authenticateToken, (req, res) => {
  // req.user is guaranteed to be present and valid
  res.json({ user: req.user });
});

// Protect an admin route
router.get('/admin/users', authenticateToken, requireAdmin, (req, res) => {
  // User is authenticated and has admin privileges
  res.json({ message: 'Admin access granted' });
});

// Optional authentication
router.get('/public-content', optionalAuth, (req, res) => {
  if (req.user) {
    // User is authenticated, show personalized content
    res.json({ message: `Hello ${req.user.username}!` });
  } else {
    // User is not authenticated, show public content
    res.json({ message: 'Hello guest!' });
  }
});
```

### Error Handling

The middleware now provides detailed error responses:

```typescript
// Example error responses
{
  "error": "Access token required"           // 401
}
{
  "error": "User not found"                  // 403
}
{
  "error": "Account has been deactivated"    // 403
}
{
  "error": "Account is not active"           // 403
}
{
  "error": "Admin access required"           // 403
}
{
  "error": "Invalid or expired token"        // 403
}
{
  "error": "Internal server error"           // 500
}
```

## Migration Steps Required

### 1. Install Dependencies

```bash
npm install mongoose @types/mongoose
```

### 2. Update Database Configuration

Replace TypeORM initialization with Mongoose:

```typescript
// In src/index.ts, replace:
import { initializeDatabase } from './config/database';

// With:
import { initializeMongoose } from './config/mongoose';

// And replace:
await initializeDatabase();

// With:
await initializeMongoose();
```

### 3. Environment Variables

Add MongoDB connection string to your `.env` file:

```env
# Add this new variable
MONGODB_URI=mongodb://localhost:27017/clubbi

# Keep existing variables
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
```

### 4. Update Services

Any services using TypeORM User repository should be updated to use Mongoose:

```typescript
// Before (TypeORM)
const user = await this.userRepository.findOne({ where: { id: userId } });

// After (Mongoose)
const user = await User.findById(userId);
```

## Benefits of the Migration

1. **Enhanced Security**: Database validation ensures tokens correspond to active, valid users
2. **Real-time Status**: User status changes are immediately reflected in authentication
3. **Better Error Handling**: Specific error messages help with debugging and user experience
4. **Performance**: Mongoose with proper indexing can be more performant for certain operations
5. **Flexibility**: MongoDB's document structure allows for more flexible user data storage
6. **Consistency**: All middleware functions now follow the same pattern of database validation

## Testing the Migration

1. **Install Dependencies**: Run `npm install mongoose @types/mongoose`
2. **Start MongoDB**: Ensure MongoDB is running locally or update `MONGODB_URI`
3. **Update Configuration**: Replace TypeORM initialization with Mongoose
4. **Test Authentication**: Verify all authentication flows work correctly
5. **Test Error Cases**: Verify proper error handling for invalid/expired tokens
6. **Test Admin Routes**: Verify admin middleware works correctly

## Backward Compatibility

The middleware maintains the same interface and behavior as before, with these improvements:
- Same function signatures
- Same request/response patterns
- Enhanced error messages
- Additional database validation

All existing routes using the middleware should continue to work without changes to the route handlers themselves.
