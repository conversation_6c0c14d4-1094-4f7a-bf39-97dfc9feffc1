import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';

// Interface for User document
export interface IUser extends Document {
  _id: mongoose.Types.ObjectId;
  username: string;
  email: string;
  mobile_number?: string;
  password: string;
  is_admin: boolean;
  is_superuser: boolean;
  is_deleted: boolean;
  is_active: boolean;
  first_name?: string;
  last_name?: string;
  date_joined: Date;
  last_login?: Date;
  
  // Methods
  validatePassword(password: string): Promise<boolean>;
  toJSON(): any;
}

// User Schema
const UserSchema = new Schema<IUser>({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    maxlength: 150
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  mobile_number: {
    type: String,
    trim: true,
    sparse: true, // Allows multiple null values but enforces uniqueness for non-null values
    match: [/^\+?[\d\s-()]+$/, 'Please enter a valid mobile number']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  is_admin: {
    type: Boolean,
    default: false
  },
  is_superuser: {
    type: Boolean,
    default: false
  },
  is_deleted: {
    type: Boolean,
    default: false
  },
  is_active: {
    type: Boolean,
    default: true
  },
  first_name: {
    type: String,
    trim: true,
    maxlength: 30
  },
  last_name: {
    type: String,
    trim: true,
    maxlength: 30
  },
  date_joined: {
    type: Date,
    default: Date.now
  },
  last_login: {
    type: Date
  }
}, {
  timestamps: true, // Adds createdAt and updatedAt fields
  collection: 'users'
});

// Indexes for better query performance
UserSchema.index({ email: 1 });
UserSchema.index({ username: 1 });
UserSchema.index({ mobile_number: 1 }, { sparse: true });
UserSchema.index({ is_deleted: 1, is_active: 1 });

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password')) return next();
  
  try {
    // Hash password with cost of 12
    const hashedPassword = await bcrypt.hash(this.password, 12);
    this.password = hashedPassword;
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Instance method to validate password
UserSchema.methods.validatePassword = async function(password: string): Promise<boolean> {
  return bcrypt.compare(password, this.password);
};

// Override toJSON to exclude password and other sensitive fields
UserSchema.methods.toJSON = function() {
  const userObject = this.toObject();
  delete userObject.password;
  return userObject;
};

// Static methods for common queries
UserSchema.statics.findByEmail = function(email: string) {
  return this.findOne({ email: email.toLowerCase() });
};

UserSchema.statics.findByUsername = function(username: string) {
  return this.findOne({ username });
};

UserSchema.statics.findByMobile = function(mobile_number: string) {
  return this.findOne({ mobile_number });
};

UserSchema.statics.findActiveUsers = function() {
  return this.find({ is_deleted: false, is_active: true });
};

UserSchema.statics.findAdmins = function() {
  return this.find({ 
    $or: [{ is_admin: true }, { is_superuser: true }],
    is_deleted: false,
    is_active: true
  });
};

// Create and export the model
export const User = mongoose.model<IUser>('User', UserSchema);
