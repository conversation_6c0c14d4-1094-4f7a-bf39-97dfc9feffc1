import { AppDataSource } from '../config/database';
import { User } from '../models/User';
import { UserSignupRequest, UserLoginRequest, LoginResponse, JwtPayload } from '../types';
import { generateToken } from '../utils/jwt';
import { isValidEmail, isValidMobile } from '../utils/validation';
import bcrypt from 'bcryptjs';

export class AuthService {
  private userRepository = AppDataSource.getRepository(User);

  async signup(userData: UserSignupRequest): Promise<{ token: string }> {
    // Check if user already exists
    const existingUser = await this.userRepository.findOne({
      where: [
        { email: userData.email },
        { username: userData.username },
        ...(userData.mobile_number ? [{ mobile_number: userData.mobile_number }] : [])
      ]
    });

    if (existingUser) {
      if (existingUser.email === userData.email) {
        throw new Error('This email is already in use.');
      }
      if (existingUser.username === userData.username) {
        throw new Error('This username is already in use.');
      }
      if (existingUser.mobile_number === userData.mobile_number) {
        throw new Error('This mobile number is already in use.');
      }
    }

    // Create new user
    const user = new User();
    user.username = userData.username;
    user.email = userData.email;
    user.mobile_number = userData.mobile_number || null;
    user.password = userData.password;

    await this.userRepository.save(user);

    // Generate JWT token
    const payload: JwtPayload = {
      userId: user.id,
      username: user.username,
      email: user.email,
      is_admin: user.is_admin
    };

    const token = generateToken(payload);
    return { token };
  }

  async login(loginData: UserLoginRequest): Promise<LoginResponse> {
    const { username, password } = loginData;

    // Determine if username is email or mobile
    let user: User | null = null;

    if (isValidEmail(username)) {
      user = await this.userRepository.findOne({ where: { email: username } });
    } else if (isValidMobile(username)) {
      user = await this.userRepository.findOne({ where: { mobile_number: username } });
    } else {
      throw new Error('Invalid credentials');
    }

    if (!user) {
      throw new Error('Invalid credentials');
    }

    // Validate password
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      throw new Error('Invalid credentials');
    }

    // Update last login
    user.last_login = new Date();
    await this.userRepository.save(user);

    // Generate JWT token
    const payload: JwtPayload = {
      userId: user.id,
      username: user.username,
      email: user.email,
      is_admin: user.is_admin
    };

    const token = generateToken(payload);

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      token
    };
  }

  async authenticateAdmin(username: string, password: string): Promise<User | null> {
    const user = await this.userRepository.findOne({ where: { username } });
    
    if (!user) {
      return null;
    }

    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      return null;
    }

    if (!user.is_admin && !user.is_superuser) {
      return null;
    }

    // Update last login
    user.last_login = new Date();
    await this.userRepository.save(user);

    return user;
  }

  async getUserById(id: number): Promise<User | null> {
    return this.userRepository.findOne({ where: { id } });
  }
}
