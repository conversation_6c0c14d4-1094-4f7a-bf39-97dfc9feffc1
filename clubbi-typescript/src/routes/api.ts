import { Router } from "express";
import { AuthController } from "../controllers/authController";
import { ClubController } from "../controllers/clubController";
import { authenticateToken, optionalAuth } from "../middleware/auth";

const router = Router();
const authController = new AuthController();
const clubController = new ClubController();

// Health check endpoint
router.get("/health", (req, res) => {
  res.json({
    status: "OK",
    timestamp: new Date().toISOString(),
    service: "Clubbi TypeScript API",
  });
});

// Authentication routes
router.post("/signup", authController.signup);
router.post("/login", authController.login);
router.get("/user-info", authenticateToken, authController.getUserInfo);

// Club routes (public access)
router.get("/search-clubs", clubController.searchClubs);
router.get("/filter-clubs", clubController.filterClubs);
router.get("/club/:club_id", clubController.getClubDetail);

// Admin club management routes (require authentication)
router.post("/clubs", authenticateToken, clubController.createClub);
router.put("/clubs/:club_id", authenticateToken, clubController.updateClub);
router.delete("/clubs/:club_id", authenticateToken, clubController.deleteClub);
router.get("/clubs", optionalAuth, clubController.getAllClubs);

export default router;
