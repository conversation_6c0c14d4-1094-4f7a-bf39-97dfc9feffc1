import { Request } from "express";

export interface UserSignupRequest {
  username: string;
  email: string;
  mobile_number?: string;
  password: string;
}

export interface UserLoginRequest {
  username: string; // Can be email or mobile number
  password: string;
}

export interface UserResponse {
  id: string; // Changed from number to string for MongoDB ObjectId
  username: string;
  email: string;
  mobile_number?: string;
  is_admin: boolean;
  is_deleted: boolean;
}

export interface LoginResponse {
  id: string; // Changed from number to string for MongoDB ObjectId
  username: string;
  email: string;
  token: string;
}

export interface ClubRequest {
  name: string;
  description: string;
  address: string;
  google_maps_link?: string;
  rating: number;
  image_url: string;
  additional_images?: string[];
  opening_hours?: Record<string, any>;
  top_pick?: boolean;
  offers?: boolean;
  nearby?: boolean;
  lower_price?: boolean;
}

export interface ClubResponse {
  id: string; // Changed from number to string for MongoDB ObjectId
  name: string;
  description: string;
  address: string;
  google_maps_link: string;
  rating: number;
  image_url: string;
  additional_images: string[];
  opening_hours: Record<string, any>;
  top_pick: boolean;
  offers: boolean;
  nearby: boolean;
  lower_price: boolean;
}

export interface ClubFilterQuery {
  rating?: number;
  top_pick?: boolean;
  offers?: boolean;
  nearby?: boolean;
  lower_price?: boolean;
}

export interface JwtPayload {
  userId: string; // Changed from number to string for MongoDB ObjectId
  username: string;
  email: string;
  is_admin: boolean;
}

export interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}
